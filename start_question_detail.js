#!/usr/bin/env node

const QuestionDetailCollector = require('./question_detail_collector');

class QuestionDetailStarter {
  constructor() {
    this.collector = new QuestionDetailCollector();
    this.limitCount = null;
    this.processedCount = 0;
  }

  // 解析命令行参数
  parseArguments() {
    const args = process.argv.slice(2);
    const options = {
      limit: null,
      help: false
    };

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      if (arg === '--limit' || arg === '-l') {
        const nextArg = args[i + 1];
        if (nextArg && !isNaN(nextArg)) {
          options.limit = parseInt(nextArg);
          i++; // 跳过下一个参数
        } else {
          console.error('错误: --limit 参数需要一个数字');
          process.exit(1);
        }
      } else if (arg === '--help' || arg === '-h') {
        options.help = true;
      } else {
        console.error(`错误: 未知参数 ${arg}`);
        this.showHelp();
        process.exit(1);
      }
    }

    return options;
  }

  // 显示帮助信息
  showHelp() {
    console.log(`
题目详情收集器启动器

使用方法:
  node start_question_detail.js [选项]

选项:
  -l, --limit <数量>    只处理指定数量的questionId
  -h, --help           显示此帮助信息

示例:
  node start_question_detail.js                    # 处理所有题目
  node start_question_detail.js --limit 10         # 只处理前10个题目
  node start_question_detail.js -l 50              # 只处理前50个题目

配置:
  输入和输出目录在 config.json 的 questionDetail 配置项中设置

注意:
  - 保存时会按照输入目录结构和文件名保存
  - 限制数量是按照发现questionId的顺序，不是按文件顺序
  - 即使限制了处理数量，所有相关文件的结构都会被保留
    `);
  }

  // 限制questionId数量的处理逻辑
  limitQuestionIds(questionIds, questionMap, fileQuestionMap, limit) {
    if (!limit || limit <= 0) {
      return { questionIds, questionMap, fileQuestionMap };
    }

    console.log(`\n🔢 应用处理限制: ${limit} 个题目`);
    
    // 只取前 limit 个 questionId
    const limitedQuestionIds = questionIds.slice(0, limit);
    const limitedQuestionIdSet = new Set(limitedQuestionIds);
    
    // 创建新的 questionMap，只包含限制的题目
    const limitedQuestionMap = new Map();
    limitedQuestionIds.forEach(questionId => {
      if (questionMap.has(questionId)) {
        limitedQuestionMap.set(questionId, questionMap.get(questionId));
      }
    });

    // 创建新的 fileQuestionMap，保持所有文件结构但只标记需要处理的题目
    const limitedFileQuestionMap = new Map();
    
    // 遍历原始的 fileQuestionMap
    for (const [filePath, questions] of fileQuestionMap.entries()) {
      // 保留所有题目，但只有在限制范围内的题目会被实际处理
      const processedQuestions = questions.map(question => {
        // 创建题目副本
        const questionCopy = { ...question };
        
        // 如果这个题目在限制范围内，标记为需要处理
        if (question.questionId && limitedQuestionIdSet.has(question.questionId)) {
          questionCopy._needsProcessing = true;
        }
        
        return questionCopy;
      });
      
      limitedFileQuestionMap.set(filePath, processedQuestions);
    }

    // 统计信息
    const affectedFiles = Array.from(limitedFileQuestionMap.keys()).filter(filePath => {
      const questions = limitedFileQuestionMap.get(filePath);
      return questions.some(q => q._needsProcessing);
    });

    console.log(`📊 限制统计:`);
    console.log(`  - 原始题目数量: ${questionIds.length}`);
    console.log(`  - 限制后处理数量: ${limitedQuestionIds.length}`);
    console.log(`  - 涉及文件数量: ${affectedFiles.length}`);
    console.log(`  - 保留文件结构: ${limitedFileQuestionMap.size} 个文件`);

    return {
      questionIds: limitedQuestionIds,
      questionMap: limitedQuestionMap,
      fileQuestionMap: limitedFileQuestionMap
    };
  }

  // 重写 collector 的 extractQuestionIds 方法来支持限制功能
  async setupLimitedProcessing(limit) {
    const originalExtractQuestionIds = this.collector.extractQuestionIds.bind(this.collector);
    
    this.collector.extractQuestionIds = async (jsonFiles) => {
      const result = await originalExtractQuestionIds(jsonFiles);
      
      if (limit && limit > 0) {
        const limitedResult = this.limitQuestionIds(
          result.questionIds,
          result.questionMap,
          result.fileQuestionMap,
          limit
        );
        
        return {
          ...result,
          ...limitedResult
        };
      }
      
      return result;
    };
  }

  // 主启动方法
  async start() {
    console.log('='.repeat(60));
    console.log('题目详情收集器启动器');
    console.log('='.repeat(60));
    
    try {
      // 解析命令行参数
      const options = this.parseArguments();
      
      if (options.help) {
        this.showHelp();
        return;
      }

      // 显示启动信息
      console.log('🚀 初始化收集器...');
      await this.collector.init();

      // 设置限制处理功能
      if (options.limit) {
        console.log(`⚙️  设置处理限制: ${options.limit} 个题目`);
        await this.setupLimitedProcessing(options.limit);
      } else {
        console.log('♾️  处理所有题目（无限制）');
      }

      // 开始处理
      console.log('\n📋 开始处理题目详情...');
      await this.collector.start();

      console.log('\n' + '='.repeat(60));
      console.log('✅ 题目详情收集完成！');
      console.log('='.repeat(60));
      
    } catch (error) {
      console.error('\n❌ 启动器运行失败:', error.message);
      if (error.stack) {
        console.error('错误堆栈:', error.stack);
      }
      process.exit(1);
    }
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n⚠️  收到中断信号，正在退出...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  收到终止信号，正在退出...');
  process.exit(0);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 如果直接运行此文件
if (require.main === module) {
  const starter = new QuestionDetailStarter();
  starter.start();
}

module.exports = QuestionDetailStarter;
