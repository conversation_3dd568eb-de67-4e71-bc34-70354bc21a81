#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

class QuestionDetailCollector {
  constructor() {
    this.collectedQuestions = [];
    this.processedFiles = 0;
    this.totalFiles = 0;
    this.questionsWithDetails = 0;
    this.totalQuestions = 0;
  }

  // 解析命令行参数
  parseArguments() {
    const args = process.argv.slice(2);
    const options = {
      inputDir: null,
      outputFile: null,
      help: false
    };

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      if (arg === '--input' || arg === '-i') {
        const nextArg = args[i + 1];
        if (nextArg && !nextArg.startsWith('-')) {
          options.inputDir = nextArg;
          i++; // 跳过下一个参数
        } else {
          console.error('错误: --input 参数需要一个目录路径');
          process.exit(1);
        }
      } else if (arg === '--output' || arg === '-o') {
        const nextArg = args[i + 1];
        if (nextArg && !nextArg.startsWith('-')) {
          options.outputFile = nextArg;
          i++; // 跳过下一个参数
        } else {
          console.error('错误: --output 参数需要一个文件路径');
          process.exit(1);
        }
      } else if (arg === '--help' || arg === '-h') {
        options.help = true;
      } else {
        console.error(`错误: 未知参数 ${arg}`);
        this.showHelp();
        process.exit(1);
      }
    }

    return options;
  }

  // 显示帮助信息
  showHelp() {
    console.log(`
题目详情收集脚本

功能: 收集指定目录下所有包含questionDetail的题目，组成一个新的JSON文件

使用方法:
  node collect_question_details.js [选项]

选项:
  -i, --input <目录>     输入目录路径（必需）
  -o, --output <文件>    输出JSON文件路径（可选，默认为collected_questions.json）
  -h, --help            显示此帮助信息

示例:
  node collect_question_details.js --input "/path/to/questions" --output "all_questions.json"
  node collect_question_details.js -i "/path/to/questions" -o "collected.json"
  node collect_question_details.js --input "/path/to/questions"

输出格式:
  生成的JSON文件包含所有有questionDetail的题目数组，格式如下：
  [
    {
      "questionId": "602865898211291136",
      "questionType": "选择题",
      "difficulty": "简单",
      "questionDetail": { ... },
      "_source": {
        "file": "相对文件路径",
        "directory": "所在目录"
      }
    }
  ]
    `);
  }

  // 递归读取目录下的所有JSON文件
  async readJsonFilesRecursively(directoryPath, baseInputPath = null) {
    const jsonFiles = [];
    const basePath = baseInputPath || directoryPath;
    
    try {
      const items = await fs.readdir(directoryPath, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(directoryPath, item.name);
        
        if (item.isDirectory()) {
          // 递归处理子目录
          const subFiles = await this.readJsonFilesRecursively(fullPath, basePath);
          jsonFiles.push(...subFiles);
        } else if (item.isFile() && item.name.endsWith('.json')) {
          try {
            const content = await fs.readFile(fullPath, 'utf8');
            const jsonData = JSON.parse(content);
            
            // 计算相对路径
            const relativePath = path.relative(basePath, fullPath);
            const directory = path.dirname(relativePath);
            
            jsonFiles.push({
              fileName: item.name,
              fullPath: fullPath,
              relativePath: relativePath,
              directory: directory === '.' ? '' : directory,
              data: jsonData
            });
          } catch (error) {
            console.error(`读取文件 ${fullPath} 失败:`, error.message);
          }
        }
      }
    } catch (error) {
      console.error(`读取目录 ${directoryPath} 失败:`, error.message);
      throw error;
    }

    return jsonFiles;
  }

  // 从JSON文件中提取包含questionDetail的题目
  extractQuestionsWithDetails(jsonFiles) {
    const questionsWithDetails = [];
    
    jsonFiles.forEach(fileInfo => {
      try {
        this.totalFiles++;
        
        // 检查文件是否是数组格式（处理后的格式）
        if (Array.isArray(fileInfo.data)) {
          fileInfo.data.forEach(question => {
            this.totalQuestions++;
            if (question.questionId && question.questionDetail) {
              questionsWithDetails.push({
                ...question,
                _source: {
                  file: fileInfo.relativePath,
                  directory: fileInfo.directory
                }
              });
              this.questionsWithDetails++;
            }
          });
        } 
        // 检查是否有原始格式的数据结构
        else if (fileInfo.data.fullResponse && 
                 fileInfo.data.fullResponse.data && 
                 fileInfo.data.fullResponse.data.list && 
                 Array.isArray(fileInfo.data.fullResponse.data.list)) {
          
          fileInfo.data.fullResponse.data.list.forEach(question => {
            this.totalQuestions++;
            if (question.questionId && question.questionDetail) {
              questionsWithDetails.push({
                ...question,
                _source: {
                  file: fileInfo.relativePath,
                  directory: fileInfo.directory
                }
              });
              this.questionsWithDetails++;
            }
          });
        }
        
        this.processedFiles++;
        
      } catch (error) {
        console.error(`处理文件 ${fileInfo.fileName} 时出错:`, error.message);
      }
    });

    return questionsWithDetails;
  }

  // 保存收集到的题目
  async saveCollectedQuestions(questions, outputFile) {
    try {
      // 确保输出目录存在
      const outputDir = path.dirname(outputFile);
      await fs.mkdir(outputDir, { recursive: true });
      
      // 保存JSON文件
      await fs.writeFile(outputFile, JSON.stringify(questions, null, 2), 'utf8');
      
      console.log(`✅ 成功保存到: ${outputFile}`);
      return true;
    } catch (error) {
      console.error(`❌ 保存文件失败: ${outputFile}`, error.message);
      return false;
    }
  }

  // 生成统计报告
  generateReport(questions, outputFile) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalFiles: this.totalFiles,
        processedFiles: this.processedFiles,
        totalQuestions: this.totalQuestions,
        questionsWithDetails: this.questionsWithDetails,
        collectedQuestions: questions.length
      },
      outputFile: outputFile,
      questionsByDirectory: {},
      questionsByType: {}
    };

    // 按目录统计
    questions.forEach(question => {
      const dir = question._source.directory || 'root';
      if (!report.questionsByDirectory[dir]) {
        report.questionsByDirectory[dir] = 0;
      }
      report.questionsByDirectory[dir]++;
    });

    // 按题目类型统计
    questions.forEach(question => {
      const type = question.questionType || 'unknown';
      if (!report.questionsByType[type]) {
        report.questionsByType[type] = 0;
      }
      report.questionsByType[type]++;
    });

    return report;
  }

  // 主处理方法
  async process(inputDir, outputFile) {
    console.log('='.repeat(60));
    console.log('题目详情收集脚本');
    console.log('='.repeat(60));
    
    const startTime = Date.now();
    
    try {
      // 验证输入目录
      try {
        const stats = await fs.stat(inputDir);
        if (!stats.isDirectory()) {
          throw new Error(`输入路径不是目录: ${inputDir}`);
        }
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error(`输入目录不存在: ${inputDir}`);
        }
        throw error;
      }

      console.log(`📂 输入目录: ${inputDir}`);
      console.log(`📄 输出文件: ${outputFile}`);
      console.log();

      // 1. 递归读取所有JSON文件
      console.log('🔍 步骤1: 扫描JSON文件...');
      const jsonFiles = await this.readJsonFilesRecursively(inputDir);
      console.log(`   找到 ${jsonFiles.length} 个JSON文件`);

      if (jsonFiles.length === 0) {
        console.log('⚠️  未找到任何JSON文件');
        return;
      }

      // 2. 提取包含questionDetail的题目
      console.log('\n📝 步骤2: 提取题目详情...');
      const questionsWithDetails = this.extractQuestionsWithDetails(jsonFiles);
      console.log(`   处理了 ${this.processedFiles} 个文件`);
      console.log(`   总题目数: ${this.totalQuestions}`);
      console.log(`   有详情的题目: ${this.questionsWithDetails}`);

      if (questionsWithDetails.length === 0) {
        console.log('⚠️  未找到任何包含questionDetail的题目');
        return;
      }

      // 3. 保存收集到的题目
      console.log('\n💾 步骤3: 保存收集结果...');
      const saved = await this.saveCollectedQuestions(questionsWithDetails, outputFile);
      
      if (!saved) {
        throw new Error('保存文件失败');
      }

      // 4. 生成并显示统计报告
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      const report = this.generateReport(questionsWithDetails, outputFile);

      console.log('\n📊 处理完成统计:');
      console.log(`   总耗时: ${duration.toFixed(2)} 秒`);
      console.log(`   处理文件: ${report.summary.processedFiles}/${report.summary.totalFiles}`);
      console.log(`   收集题目: ${report.summary.collectedQuestions}/${report.summary.totalQuestions}`);
      console.log(`   收集率: ${((report.summary.collectedQuestions / report.summary.totalQuestions) * 100).toFixed(1)}%`);

      // 显示按目录的统计
      if (Object.keys(report.questionsByDirectory).length > 1) {
        console.log('\n📁 按目录统计:');
        Object.entries(report.questionsByDirectory)
          .sort(([,a], [,b]) => b - a)
          .forEach(([dir, count]) => {
            console.log(`   ${dir || 'root'}: ${count} 个题目`);
          });
      }

      // 显示按类型的统计
      if (Object.keys(report.questionsByType).length > 1) {
        console.log('\n📋 按类型统计:');
        Object.entries(report.questionsByType)
          .sort(([,a], [,b]) => b - a)
          .forEach(([type, count]) => {
            console.log(`   ${type}: ${count} 个题目`);
          });
      }

      // 保存统计报告
      const reportFile = outputFile.replace(/\.json$/, '_report.json');
      await fs.writeFile(reportFile, JSON.stringify(report, null, 2), 'utf8');
      console.log(`\n📈 统计报告已保存到: ${reportFile}`);

      console.log('\n' + '='.repeat(60));
      console.log('✅ 题目详情收集完成！');
      console.log('='.repeat(60));

    } catch (error) {
      console.error('\n❌ 处理失败:', error.message);
      throw error;
    }
  }

  // 主启动方法
  async start() {
    try {
      // 解析命令行参数
      const options = this.parseArguments();
      
      if (options.help) {
        this.showHelp();
        return;
      }

      if (!options.inputDir) {
        console.error('错误: 必须指定输入目录');
        console.log('使用 --help 查看使用说明');
        process.exit(1);
      }

      // 设置默认输出文件
      const outputFile = options.outputFile || 'collected_questions.json';

      // 开始处理
      await this.process(options.inputDir, outputFile);
      
    } catch (error) {
      console.error('运行失败:', error.message);
      process.exit(1);
    }
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n⚠️  收到中断信号，正在退出...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  收到终止信号，正在退出...');
  process.exit(0);
});

// 如果直接运行此文件
if (require.main === module) {
  const collector = new QuestionDetailCollector();
  collector.start();
}

module.exports = QuestionDetailCollector;
