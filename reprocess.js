// 重新处理脚本
const { QuestionDetailCollector } = require('./question_detail_collector');

async function reprocess() {
  console.log('🚀 开始重新处理...');
  
  const collector = new QuestionDetailCollector();
  
  // 设置较小的并发数以避免问题
  collector.maxConcurrent = 2;
  collector.maxRetries = 2;
  
  try {
    await collector.processAllFiles();
    console.log('✅ 重新处理完成！');
  } catch (error) {
    console.error('❌ 重新处理失败:', error.message);
  }
}

reprocess().catch(console.error);